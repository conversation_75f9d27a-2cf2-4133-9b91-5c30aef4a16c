#!/bin/bash

# RBAC API Test Script using curl
# This script tests all RBAC endpoints

BASE_URL="http://localhost:3000"
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 RBAC API Test Suite${NC}"
echo "=========================="
echo "Base URL: $BASE_URL"
echo ""

# Function to test an endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local method="${3:-GET}"
    local expected_status="${4:-200}"
    local cookie="$5"
    
    echo -e "${BLUE}🧪 Testing: $name${NC}"
    echo "   URL: $url"
    echo "   Method: $method"
    
    # Build curl command
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$cookie" ]; then
        curl_cmd="$curl_cmd -H 'Cookie: $cookie'"
    fi
    
    curl_cmd="$curl_cmd '$BASE_URL$url'"
    
    # Execute curl and capture response
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    # Check status
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "   Status: ${GREEN}$status_code${NC} (expected: $expected_status) ✅"
    else
        echo -e "   Status: ${RED}$status_code${NC} (expected: $expected_status) ❌"
    fi
    
    # Show response body (first 200 chars)
    if [ ! -z "$body" ] && [ "$body" != "null" ]; then
        local short_body=$(echo "$body" | cut -c1-200)
        echo "   Response: $short_body"
        if [ ${#body} -gt 200 ]; then
            echo "   ..."
        fi
    fi
    
    echo ""
    
    # Return success/failure
    [ "$status_code" = "$expected_status" ]
}

# Test server connectivity
echo -e "${YELLOW}📡 Testing server connectivity...${NC}"
if ! curl -s --connect-timeout 5 "$BASE_URL/api/health" > /dev/null; then
    echo -e "${RED}❌ Server is not responding. Please ensure the application is running.${NC}"
    echo "   Tried to connect to: $BASE_URL"
    exit 1
fi
echo -e "${GREEN}✅ Server is running${NC}"
echo ""

# Test unauthenticated access (should return 401)
echo -e "${YELLOW}🔒 Testing unauthenticated access (should fail with 401)...${NC}"
test_endpoint "Users (no auth)" "/api/rbac/users?limit=1" "GET" "401"
test_endpoint "Roles (no auth)" "/api/rbac/roles" "GET" "401"
test_endpoint "Audit (no auth)" "/api/rbac/audit?limit=1" "GET" "401"

# Instructions for getting session cookie
echo -e "${YELLOW}📋 To test authenticated endpoints:${NC}"
echo "1. Open browser and go to: $BASE_URL/login"
echo "2. Log in with an admin account"
echo "3. Open developer tools (F12)"
echo "4. Go to Application/Storage > Cookies > $BASE_URL"
echo "5. Copy the session cookie value"
echo ""

# Check if session cookie is provided as argument
if [ ! -z "$1" ]; then
    SESSION_COOKIE="$1"
    echo -e "${GREEN}✅ Using provided session cookie${NC}"
    echo ""
    
    # Test authenticated endpoints
    echo -e "${YELLOW}🔐 Testing authenticated RBAC endpoints...${NC}"
    
    # Track results
    PASSED=0
    FAILED=0
    
    # RBAC Init endpoints
    if test_endpoint "RBAC Init Status" "/api/rbac/init" "GET" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    if test_endpoint "RBAC Initialize" "/api/rbac/init" "POST" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    # Users endpoints
    if test_endpoint "Users List" "/api/rbac/users?limit=5" "GET" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    # Roles endpoints
    if test_endpoint "Roles List" "/api/rbac/roles" "GET" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    # Audit endpoints
    if test_endpoint "Audit Log" "/api/rbac/audit?limit=5" "GET" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    # Permissions endpoints
    if test_endpoint "Permissions List" "/api/rbac/permissions" "GET" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    # Permission checking
    if test_endpoint "Check Permission" "/api/rbac/check-permission?permission=read:user" "GET" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    if test_endpoint "User Permissions" "/api/rbac/user-permissions" "GET" "200" "$SESSION_COOKIE"; then
        ((PASSED++))
    else
        ((FAILED++))
    fi
    
    # Results summary
    TOTAL=$((PASSED + FAILED))
    SUCCESS_RATE=$(echo "scale=1; $PASSED * 100 / $TOTAL" | bc -l 2>/dev/null || echo "0")
    
    echo -e "${BLUE}📊 Results Summary:${NC}"
    echo "   ✅ Passed: $PASSED"
    echo "   ❌ Failed: $FAILED"
    echo "   📈 Total: $TOTAL"
    echo "   🎯 Success Rate: $SUCCESS_RATE%"
    
    if [ $FAILED -eq 0 ]; then
        echo -e "${GREEN}🎉 All RBAC APIs are functioning correctly!${NC}"
    else
        echo -e "${YELLOW}⚠️  Some RBAC APIs need attention.${NC}"
    fi
    
else
    echo -e "${YELLOW}⚠️  No session cookie provided. Skipping authenticated tests.${NC}"
    echo ""
    echo "Usage: $0 [session_cookie]"
    echo "Example: $0 'next-auth.session-token=eyJhbGciOiJIUzI1NiJ9...'"
fi

echo ""
echo -e "${BLUE}🎯 Next Steps:${NC}"
echo "   - If tests are failing, check server logs for errors"
echo "   - Ensure RBAC system is initialized: npm run rbac:init"
echo "   - Check database connectivity and schema"
echo "   - Verify user has appropriate permissions"
