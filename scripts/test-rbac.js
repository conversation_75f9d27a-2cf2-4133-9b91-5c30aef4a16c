#!/usr/bin/env node

/**
 * RBAC Test Script
 * 
 * This script tests the RBAC API endpoints to verify functionality and access control
 */

const https = require('https');
const http = require('http');

class RbacTester {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
    this.testResults = [];
  }

  /**
   * Make an HTTP request
   */
  async makeRequest(path, method = 'GET', body = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const url = new URL(path, this.baseUrl);
      const isHttps = url.protocol === 'https:';
      const client = isHttps ? https : http;

      const options = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      };

      const req = client.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            const jsonData = data ? JSON.parse(data) : {};
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: jsonData,
            });
          } catch (error) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: data,
            });
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (body) {
        req.write(JSON.stringify(body));
      }

      req.end();
    });
  }

  /**
   * Test RBAC initialization
   */
  async testRbacInit() {
    console.log('\n🔧 Testing RBAC Initialization...');
    
    try {
      const response = await this.makeRequest('/api/rbac/init', 'POST');
      
      if (response.status === 401) {
        console.log('✅ RBAC Init properly requires authentication');
        return true;
      } else if (response.status === 403) {
        console.log('✅ RBAC Init properly requires admin role');
        return true;
      } else if (response.status === 200) {
        console.log('✅ RBAC Init successful (admin user authenticated)');
        return true;
      } else {
        console.log(`❌ Unexpected status: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ Error testing RBAC init: ${error.message}`);
      return false;
    }
  }

  /**
   * Test permission checking endpoint
   */
  async testPermissionCheck() {
    console.log('\n🔍 Testing Permission Check...');

    try {
      const response = await this.makeRequest('/api/rbac/check-permission?permission=read:user', 'GET');
      
      if (response.status === 401) {
        console.log('✅ Permission check properly requires authentication');
        return true;
      } else if (response.status === 200) {
        console.log('✅ Permission check endpoint accessible');
        console.log(`   Response: ${JSON.stringify(response.data)}`);
        return true;
      } else {
        console.log(`❌ Unexpected status: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ Error testing permission check: ${error.message}`);
      return false;
    }
  }

  /**
   * Test user permissions endpoint
   */
  async testUserPermissions() {
    console.log('\n👤 Testing User Permissions...');
    
    try {
      const response = await this.makeRequest('/api/rbac/user-permissions', 'GET');
      
      if (response.status === 401) {
        console.log('✅ User permissions properly requires authentication');
        return true;
      } else if (response.status === 200) {
        console.log('✅ User permissions endpoint accessible');
        return true;
      } else {
        console.log(`❌ Unexpected status: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ Error testing user permissions: ${error.message}`);
      return false;
    }
  }

  /**
   * Test roles endpoint
   */
  async testRoles() {
    console.log('\n🎭 Testing Roles Endpoint...');
    
    try {
      const response = await this.makeRequest('/api/rbac/roles', 'GET');
      
      if (response.status === 401) {
        console.log('✅ Roles endpoint properly requires authentication');
        return true;
      } else if (response.status === 403) {
        console.log('✅ Roles endpoint properly requires admin/org-admin role');
        return true;
      } else if (response.status === 200) {
        console.log('✅ Roles endpoint accessible (admin user authenticated)');
        return true;
      } else {
        console.log(`❌ Unexpected status: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ Error testing roles: ${error.message}`);
      return false;
    }
  }

  /**
   * Test users endpoint
   */
  async testUsers() {
    console.log('\n👥 Testing Users Endpoint...');
    
    try {
      const response = await this.makeRequest('/api/rbac/users', 'GET');
      
      if (response.status === 401) {
        console.log('✅ Users endpoint properly requires authentication');
        return true;
      } else if (response.status === 403) {
        console.log('✅ Users endpoint properly requires admin/org-admin role');
        return true;
      } else if (response.status === 200) {
        console.log('✅ Users endpoint accessible (admin user authenticated)');
        return true;
      } else {
        console.log(`❌ Unexpected status: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ Error testing users: ${error.message}`);
      return false;
    }
  }

  /**
   * Test comprehensive RBAC test endpoint
   */
  async testRbacTestSuite() {
    console.log('\n🧪 Testing RBAC Test Suite...');
    
    try {
      const response = await this.makeRequest('/api/rbac/test', 'POST', {
        testType: 'all',
        cleanup: true,
        verbose: true,
      });
      
      if (response.status === 401) {
        console.log('✅ RBAC test suite properly requires authentication');
        return true;
      } else if (response.status === 403) {
        console.log('✅ RBAC test suite properly requires admin role');
        return true;
      } else if (response.status === 200) {
        console.log('✅ RBAC test suite executed successfully');
        console.log(`   Summary: ${JSON.stringify(response.data.summary, null, 2)}`);
        
        // Show detailed results if available
        if (response.data.results) {
          Object.keys(response.data.results).forEach(testType => {
            const results = response.data.results[testType];
            const passed = results.filter(r => r.passed).length;
            const total = results.length;
            console.log(`   ${testType}: ${passed}/${total} tests passed`);
          });
        }
        
        return true;
      } else {
        console.log(`❌ Unexpected status: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(response.data)}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ Error testing RBAC test suite: ${error.message}`);
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting RBAC API Tests...');
    console.log(`   Base URL: ${this.baseUrl}`);
    
    const tests = [
      { name: 'RBAC Init', fn: () => this.testRbacInit() },
      { name: 'Permission Check', fn: () => this.testPermissionCheck() },
      { name: 'User Permissions', fn: () => this.testUserPermissions() },
      { name: 'Roles', fn: () => this.testRoles() },
      { name: 'Users', fn: () => this.testUsers() },
      { name: 'RBAC Test Suite', fn: () => this.testRbacTestSuite() },
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      try {
        const result = await test.fn();
        if (result) {
          passed++;
        } else {
          failed++;
        }
      } catch (error) {
        console.log(`❌ ${test.name} failed with error: ${error.message}`);
        failed++;
      }
    }

    console.log('\n📊 Test Summary:');
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📈 Total: ${passed + failed}`);

    if (failed === 0) {
      console.log('\n🎉 All tests passed! RBAC API is functioning correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the RBAC implementation.');
    }

    return { passed, failed, total: passed + failed };
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const baseUrl = args[0] || 'http://localhost:3000';
  
  console.log('🔐 RBAC API Test Runner');
  console.log('========================');
  
  const tester = new RbacTester(baseUrl);
  await tester.runAllTests();
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { RbacTester };
