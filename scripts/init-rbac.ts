#!/usr/bin/env tsx

/**
 * RBAC Initialization Script
 * 
 * This script initializes the RBAC system and optionally runs tests
 * 
 * Usage:
 *   npx tsx scripts/init-rbac.ts
 *   npx tsx scripts/init-rbac.ts --test
 *   npx tsx scripts/init-rbac.ts --test --cleanup
 */

import { initializeRbacSystem, isRbacInitialized } from '../src/lib/rbac/init-rbac';
import { runRbacTests, cleanupTestData } from '../src/lib/rbac/test-rbac';
import { logger } from '../src/lib/logger';

async function main() {
  const args = process.argv.slice(2);
  const shouldTest = args.includes('--test');
  const shouldCleanup = args.includes('--cleanup');

  console.log('🚀 RBAC System Initialization');
  console.log('================================');

  try {
    // Check if already initialized
    const isInitialized = await isRbacInitialized();
    
    if (isInitialized) {
      console.log('✅ RBAC system is already initialized');
    } else {
      console.log('🔧 Initializing RBAC system...');
      await initializeRbacSystem();
      console.log('✅ RBAC system initialized successfully');
    }

    // Run tests if requested
    if (shouldTest) {
      console.log('\n🧪 Running RBAC Tests');
      console.log('=====================');
      
      const testResults = await runRbacTests();
      
      const totalTests = testResults.length;
      const passedTests = testResults.filter(r => r.passed).length;
      const failedTests = totalTests - passedTests;
      
      console.log(`\n📊 Test Results:`);
      console.log(`   Total: ${totalTests}`);
      console.log(`   Passed: ${passedTests} ✅`);
      console.log(`   Failed: ${failedTests} ❌`);
      console.log(`   Success Rate: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`);
      
      // Show detailed results
      console.log('\n📋 Detailed Results:');
      testResults.forEach((result, index) => {
        const status = result.passed ? '✅' : '❌';
        console.log(`   ${index + 1}. ${status} ${result.name}`);
        if (!result.passed && result.error) {
          console.log(`      Error: ${result.error}`);
        }
        if (result.details) {
          console.log(`      Details: ${JSON.stringify(result.details, null, 2)}`);
        }
      });

      // Clean up test data if requested
      if (shouldCleanup) {
        console.log('\n🧹 Cleaning up test data...');
        await cleanupTestData();
        console.log('✅ Test data cleaned up');
      }

      // Exit with error code if tests failed
      if (failedTests > 0) {
        console.log('\n❌ Some tests failed. Please review the results above.');
        process.exit(1);
      }
    }

    console.log('\n🎉 RBAC system is ready!');
    console.log('\nNext steps:');
    console.log('1. Start your application');
    console.log('2. Log in as an Organization Admin');
    console.log('3. Navigate to "Role & Access Management"');
    console.log('4. Create custom roles and manage users');

  } catch (error) {
    console.error('❌ Error during RBAC initialization:', error);
    logger.error('RBAC initialization failed:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the script
main().catch((error) => {
  console.error('Script execution failed:', error);
  process.exit(1);
});
