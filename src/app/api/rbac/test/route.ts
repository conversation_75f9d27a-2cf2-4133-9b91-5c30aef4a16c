import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { runRbacTests, cleanupTestData } from '@/lib/rbac/test-rbac';
import { RbacApiTestSuite } from '@/lib/rbac/api-test-suite';
import { HttpTestRunner } from '@/lib/rbac/http-test-runner';

/**
 * POST /api/rbac/test
 * Run RBAC system tests (Admin only)
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin (only admins can run tests)
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await req.json();
    const {
      cleanup = false,
      testType = 'all', // 'basic', 'api', 'http', 'all'
      verbose = false
    } = body;

    const results: any = {
      timestamp: new Date().toISOString(),
      testType,
      results: {},
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Run basic RBAC tests
    if (testType === 'basic' || testType === 'all') {
      logger.info('Running basic RBAC tests...');
      const basicResults = await runRbacTests();
      results.results.basic = basicResults;
      results.summary.total += basicResults.length;
      results.summary.passed += basicResults.filter((r: any) => r.passed).length;
      results.summary.failed += basicResults.filter((r: any) => !r.passed).length;
    }

    // Run API tests
    if (testType === 'api' || testType === 'all') {
      logger.info('Running RBAC API tests...');
      const apiTestSuite = new RbacApiTestSuite();
      await apiTestSuite.setup();
      const apiResults = await apiTestSuite.runTests();
      results.results.api = apiResults;
      results.summary.total += apiResults.length;
      results.summary.passed += apiResults.filter(r => r.passed).length;
      results.summary.failed += apiResults.filter(r => !r.passed).length;

      if (cleanup) {
        await apiTestSuite.cleanup();
      }
    }

    // Run HTTP tests
    if (testType === 'http' || testType === 'all') {
      logger.info('Running RBAC HTTP tests...');
      const httpTestRunner = new HttpTestRunner();
      const httpResults = await httpTestRunner.runHttpTests();
      results.results.http = httpResults;
      results.summary.total += httpResults.length;
      results.summary.passed += httpResults.filter(r => r.passed).length;
      results.summary.failed += httpResults.filter(r => !r.passed).length;

      if (cleanup) {
        await httpTestRunner.cleanup();
      }
    }

    // Clean up basic test data if requested
    if (cleanup && (testType === 'basic' || testType === 'all')) {
      await cleanupTestData();
    }

    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    logger.info('RBAC tests completed', {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      runBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
      },
      results: testResults,
      cleanupPerformed: cleanup,
    });
  } catch (error) {
    logger.error('Error running RBAC tests:', error);
    return NextResponse.json(
      { error: 'Failed to run RBAC tests' },
      { status: 500 }
    );
  }
}
