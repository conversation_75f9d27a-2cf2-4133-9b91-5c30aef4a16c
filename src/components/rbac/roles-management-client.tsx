"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  UserCog, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Users,
  Key,
  Shield,
  Calendar,
} from "lucide-react";
import { toast } from "sonner";
import { CreateRoleDialog } from "@/components/rbac/create-role-dialog";
import { EditRoleDialog } from "@/components/rbac/edit-role-dialog";

interface Permission {
  name: string;
  displayName: string;
  description?: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  parentRole?: {
    id: string;
    name: string;
    displayName: string;
  };
  childRoles: Array<{
    id: string;
    name: string;
    displayName: string;
  }>;
  permissions: Permission[];
  userCount: number;
  users: Array<{
    name: string;
    email: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export function RolesManagementClient() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/rbac/roles");
      if (!response.ok) {
        throw new Error("Failed to fetch roles");
      }
      const data = await response.json();
      setRoles(data.data);
    } catch (error) {
      console.error("Error fetching roles:", error);
      toast.error("Failed to fetch roles");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  const handleDeleteRole = async () => {
    if (!selectedRole) return;

    try {
      const response = await fetch(`/api/rbac/roles/${selectedRole.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete role");
      }

      toast.success("Role deleted successfully");
      setShowDeleteDialog(false);
      setSelectedRole(null);
      fetchRoles();
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete role");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const groupPermissionsByCategory = (permissions: Permission[]) => {
    return permissions.reduce((acc, permission) => {
      const category = permission.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Roles</h2>
          <p className="text-muted-foreground">
            Create and manage custom roles with specific permissions
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Role
        </Button>
      </div>

      {/* Roles Grid */}
      {loading ? (
        <div className="text-center py-8">Loading roles...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {roles.map((role) => (
            <Card key={role.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="flex items-center gap-2">
                      <UserCog className="h-5 w-5" />
                      {role.displayName}
                      {role.isSystemRole && (
                        <Badge variant="secondary" className="text-xs">
                          System
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {role.description || "No description provided"}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedRole(role);
                          setShowEditDialog(true);
                        }}
                        disabled={role.isSystemRole}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Role
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedRole(role);
                          setShowDeleteDialog(true);
                        }}
                        className="text-destructive"
                        disabled={role.isSystemRole || role.userCount > 0}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Role
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{role.userCount} users</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Key className="h-4 w-4 text-muted-foreground" />
                    <span>{role.permissions.length} permissions</span>
                  </div>
                </div>

                {/* Permissions Preview */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Permissions</h4>
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(groupPermissionsByCategory(role.permissions))
                      .slice(0, 3)
                      .map(([category, perms]) => (
                        <Badge key={category} variant="outline" className="text-xs">
                          {category} ({perms.length})
                        </Badge>
                      ))}
                    {Object.keys(groupPermissionsByCategory(role.permissions)).length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{Object.keys(groupPermissionsByCategory(role.permissions)).length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Hierarchy */}
                {role.parentRole && (
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium">Parent Role</h4>
                    <Badge variant="outline" className="text-xs">
                      {role.parentRole.displayName}
                    </Badge>
                  </div>
                )}

                {role.childRoles.length > 0 && (
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium">Child Roles</h4>
                    <div className="flex flex-wrap gap-1">
                      {role.childRoles.slice(0, 2).map((childRole) => (
                        <Badge key={childRole.id} variant="outline" className="text-xs">
                          {childRole.displayName}
                        </Badge>
                      ))}
                      {role.childRoles.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{role.childRoles.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Created Date */}
                <div className="flex items-center gap-2 text-xs text-muted-foreground pt-2 border-t">
                  <Calendar className="h-3 w-3" />
                  Created {formatDate(role.createdAt)}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {roles.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-8">
            <UserCog className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No custom roles found</h3>
            <p className="text-muted-foreground mb-4">
              Create your first custom role to get started with role-based access control.
            </p>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Role
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Dialogs */}
      <CreateRoleDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => {
          fetchRoles();
          setShowCreateDialog(false);
        }}
      />

      {selectedRole && (
        <>
          <EditRoleDialog
            open={showEditDialog}
            onOpenChange={setShowEditDialog}
            role={selectedRole}
            onSuccess={() => {
              fetchRoles();
              setShowEditDialog(false);
              setSelectedRole(null);
            }}
          />

          <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Role</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete the role "{selectedRole.displayName}"?
                  This action cannot be undone.
                  {selectedRole.userCount > 0 && (
                    <span className="block mt-2 text-destructive">
                      This role is assigned to {selectedRole.userCount} user(s) and cannot be deleted.
                    </span>
                  )}
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteDialog(false)}
                >
                  Cancel
                </Button>
                <Button 
                  variant="destructive" 
                  onClick={handleDeleteRole}
                  disabled={selectedRole.userCount > 0}
                >
                  Delete Role
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
}
