#!/usr/bin/env node

/**
 * Simple RBAC API Test with Browser Session
 * This script tests RBAC APIs by instructing the user to authenticate in browser first
 */

const fetch = require('node-fetch');
const readline = require('readline');

const BASE_URL = 'http://localhost:3000';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function makeRequest(url, options = {}) {
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`;
  
  try {
    const response = await fetch(fullUrl, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json().catch(() => ({}));
    
    return {
      status: response.status,
      ok: response.ok,
      data,
      headers: response.headers
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message,
      data: {}
    };
  }
}

async function testEndpoint(name, url, options = {}, expectedStatus = 200) {
  console.log(`\n🧪 Testing: ${name}`);
  console.log(`   URL: ${url}`);
  
  try {
    const response = await makeRequest(url, options);
    
    const statusColor = response.status === expectedStatus ? '\x1b[32m' : '\x1b[31m';
    const resetColor = '\x1b[0m';
    
    console.log(`   Status: ${statusColor}${response.status}${resetColor} (expected: ${expectedStatus})`);
    
    if (response.data.error) {
      console.log(`   Error: ${response.data.error}`);
    }
    
    if (response.data.success !== undefined) {
      console.log(`   Success: ${response.data.success}`);
    }
    
    if (response.data.data) {
      if (Array.isArray(response.data.data)) {
        console.log(`   Data: Array with ${response.data.data.length} items`);
      } else if (typeof response.data.data === 'object') {
        const keys = Object.keys(response.data.data);
        console.log(`   Data: Object with keys: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`);
      }
    }
    
    return response.status === expectedStatus;
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return false;
  }
}

async function testWithCookie(sessionCookie) {
  console.log('\n🔐 Testing RBAC APIs with authentication...');
  
  const headers = {
    'Cookie': sessionCookie
  };
  
  const tests = [
    // Basic RBAC endpoints
    ['RBAC Init Status', '/api/rbac/init', { headers }, 200],
    ['RBAC Users List', '/api/rbac/users?limit=5', { headers }, 200],
    ['RBAC Roles List', '/api/rbac/roles', { headers }, 200],
    ['RBAC Audit Log', '/api/rbac/audit?limit=5', { headers }, 200],
    ['RBAC Permissions', '/api/rbac/permissions', { headers }, 200],
    
    // Permission checking
    ['Check Permission', '/api/rbac/check-permission?permission=read:user', { headers }, 200],
    ['User Permissions', '/api/rbac/user-permissions', { headers }, 200],
    
    // POST endpoints
    ['RBAC Initialize', '/api/rbac/init', { method: 'POST', headers }, 200],
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const [name, url, options, expectedStatus] of tests) {
    const success = await testEndpoint(name, url, options, expectedStatus);
    if (success) {
      passed++;
    } else {
      failed++;
    }
  }
  
  console.log('\n📊 Results Summary:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Total: ${passed + failed}`);
  console.log(`   🎯 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  return { passed, failed };
}

async function main() {
  console.log('🔐 RBAC API Test with Browser Authentication');
  console.log('='.repeat(50));
  
  // Test server connectivity
  console.log('\n📡 Testing server connectivity...');
  const healthCheck = await makeRequest('/api/health');
  if (!healthCheck.ok) {
    console.log('❌ Server is not responding. Please ensure the application is running.');
    console.log(`   Tried to connect to: ${BASE_URL}`);
    process.exit(1);
  }
  console.log('✅ Server is running');
  
  // Test unauthenticated access
  console.log('\n🔒 Testing unauthenticated access (should fail)...');
  await testEndpoint('Users (no auth)', '/api/rbac/users', {}, 401);
  await testEndpoint('Roles (no auth)', '/api/rbac/roles', {}, 401);
  await testEndpoint('Audit (no auth)', '/api/rbac/audit', {}, 401);
  
  console.log('\n📋 Instructions for authenticated testing:');
  console.log('1. Open your browser and go to: http://localhost:3000/login');
  console.log('2. Log in with an admin account');
  console.log('3. Open browser developer tools (F12)');
  console.log('4. Go to Application/Storage > Cookies > http://localhost:3000');
  console.log('5. Find the session cookie (next-auth.session-token or __Secure-next-auth.session-token)');
  console.log('6. Copy the entire cookie string (name=value)');
  
  const sessionCookie = await question('\nPaste the session cookie here (or press Enter to skip): ');
  
  if (sessionCookie.trim()) {
    await testWithCookie(sessionCookie.trim());
  } else {
    console.log('\n⚠️  Skipping authenticated tests. To test with authentication:');
    console.log('   - Follow the instructions above to get a session cookie');
    console.log('   - Run this script again with the cookie');
  }
  
  console.log('\n🎯 Next Steps:');
  console.log('   - If tests are failing, check the server logs for errors');
  console.log('   - Ensure RBAC system is initialized: npm run rbac:init');
  console.log('   - Check database connectivity and schema');
  console.log('   - Verify user has appropriate permissions');
  
  rl.close();
}

if (require.main === module) {
  main().catch(console.error);
}
