#!/usr/bin/env node

/**
 * Test the RBAC Audit API
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const db = new PrismaClient();

async function createTestUserWithPermission() {
  console.log('🔧 Creating test user with audit permissions...');
  
  try {
    // Find or create organization
    let organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      organization = await db.organization.create({
        data: {
          name: 'Test Organization',
          status: 'ACTIVE',
          verificationStatus: 'VERIFIED',
        }
      });
    }
    
    // Create admin user with audit permissions
    const userExists = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!userExists) {
      const hashedPassword = await bcrypt.hash('test123', 12);
      const user = await db.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Audit Test User',
          password: hashedPassword,
          role: 'ORGANIZATION_ADMIN',
          organizationId: organization.id,
          emailVerified: new Date(),
        }
      });
      console.log('✅ Created test user: <EMAIL> / test123');
      return user;
    } else {
      console.log('✅ Test user already exists');
      return userExists;
    }
    
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  }
}

async function checkPermissionExists() {
  console.log('🔧 Checking if audit permission exists...');
  
  try {
    const permission = await db.permission.findUnique({
      where: { name: 'view:rbac:audit' }
    });
    
    if (permission) {
      console.log('✅ Permission view:rbac:audit exists');
      return true;
    } else {
      console.log('❌ Permission view:rbac:audit does not exist');
      
      // Check what audit permissions exist
      const auditPermissions = await db.permission.findMany({
        where: { 
          OR: [
            { name: { contains: 'audit' } },
            { name: { contains: 'rbac' } }
          ]
        },
        select: { name: true, displayName: true }
      });
      
      console.log('Available audit/rbac permissions:');
      auditPermissions.forEach(p => console.log(`  - ${p.name}: ${p.displayName}`));
      
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking permission:', error);
    return false;
  }
}

async function createSampleAuditData() {
  console.log('🔧 Creating sample audit data...');
  
  try {
    // Get a user and permission for sample data
    const user = await db.user.findFirst();
    const permission = await db.permission.findFirst();
    
    if (!user || !permission) {
      console.log('⚠️  No users or permissions found, skipping sample data creation');
      return;
    }
    
    // Create some sample permission usage logs
    const existingLogs = await db.permissionUsageLog.count();
    
    if (existingLogs === 0) {
      await db.permissionUsageLog.createMany({
        data: [
          {
            userId: user.id,
            permissionId: permission.id,
            action: 'API Access',
            success: true,
            ipAddress: '127.0.0.1',
            userAgent: 'Test Agent',
            metadata: { test: true }
          },
          {
            userId: user.id,
            permissionId: permission.id,
            action: 'Dashboard View',
            success: true,
            ipAddress: '127.0.0.1',
            userAgent: 'Test Agent',
            metadata: { test: true }
          }
        ]
      });
      console.log('✅ Created sample audit data');
    } else {
      console.log('✅ Sample audit data already exists');
    }
    
  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  }
}

async function testAuditAPIDirectly() {
  console.log('🔧 Testing audit API logic directly...');
  
  try {
    // Test the database queries directly
    const logs = await db.permissionUsageLog.findMany({
      orderBy: { timestamp: 'desc' },
      take: 5,
    });
    
    console.log(`✅ Found ${logs.length} permission usage logs`);
    
    if (logs.length > 0) {
      console.log('Sample log:', {
        id: logs[0].id,
        userId: logs[0].userId,
        permissionId: logs[0].permissionId,
        action: logs[0].action,
        success: logs[0].success,
        timestamp: logs[0].timestamp
      });
    }
    
    // Test role changes query
    const roleChanges = await db.userCustomRole.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
      take: 5,
    });
    
    console.log(`✅ Found ${roleChanges.length} role assignments`);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing audit API logic:', error);
    console.error('Error details:', error.message);
    return false;
  }
}

async function main() {
  console.log('🔐 RBAC Audit API Test');
  console.log('======================');
  
  try {
    await db.$connect();
    console.log('✅ Database connected');
    
    // Check if permission exists
    const permissionExists = await checkPermissionExists();
    
    if (!permissionExists) {
      console.log('❌ Required permission does not exist. Run RBAC initialization first.');
      console.log('   Command: npm run rbac:init');
      return;
    }
    
    // Create test user
    await createTestUserWithPermission();
    
    // Create sample data
    await createSampleAuditData();
    
    // Test API logic
    const apiTestPassed = await testAuditAPIDirectly();
    
    if (apiTestPassed) {
      console.log('\n🎉 Audit API test completed successfully!');
      console.log('\nTo test the full API:');
      console.log('1. Start the server: npm run dev');
      console.log('2. Log in as: <EMAIL> / test123');
      console.log('3. Visit: http://localhost:3000/dashboard/rbac/audit');
      console.log('4. Or test API: curl with session cookie');
    } else {
      console.log('\n❌ Audit API test failed. Check the errors above.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}
