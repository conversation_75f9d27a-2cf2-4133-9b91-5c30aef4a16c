#!/usr/bin/env node

/**
 * Test the RBAC Roles API
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const db = new PrismaClient();

async function checkPermissionExists() {
  console.log('🔧 Checking if roles permission exists...');
  
  try {
    const permission = await db.permission.findUnique({
      where: { name: 'read:role' }
    });
    
    if (permission) {
      console.log('✅ Permission read:role exists');
      return true;
    } else {
      console.log('❌ Permission read:role does not exist');
      
      // Check what role permissions exist
      const rolePermissions = await db.permission.findMany({
        where: { 
          OR: [
            { name: { contains: 'role' } },
            { name: { contains: 'rbac' } }
          ]
        },
        select: { name: true, displayName: true }
      });
      
      console.log('Available role/rbac permissions:');
      rolePermissions.forEach(p => console.log(`  - ${p.name}: ${p.displayName}`));
      
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking permission:', error);
    return false;
  }
}

async function createTestUserWithRolePermission() {
  console.log('🔧 Creating test user with role permissions...');
  
  try {
    // Find or create organization
    let organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      organization = await db.organization.create({
        data: {
          name: 'Test Organization',
          status: 'ACTIVE',
          verificationStatus: 'VERIFIED',
        }
      });
    }
    
    // Create admin user with role permissions
    const userExists = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!userExists) {
      const hashedPassword = await bcrypt.hash('test123', 12);
      const user = await db.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Roles Test User',
          password: hashedPassword,
          role: 'ORGANIZATION_ADMIN',
          organizationId: organization.id,
          emailVerified: new Date(),
        }
      });
      console.log('✅ Created test user: <EMAIL> / test123');
      return user;
    } else {
      console.log('✅ Test user already exists');
      return userExists;
    }
    
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  }
}

async function createSampleRoles() {
  console.log('🔧 Creating sample roles...');
  
  try {
    // Get organization
    const organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      console.log('❌ No test organization found');
      return;
    }
    
    // Check if sample roles already exist
    const existingRoles = await db.customRole.count({
      where: { organizationId: organization.id }
    });
    
    if (existingRoles === 0) {
      // Create sample roles
      await db.customRole.createMany({
        data: [
          {
            name: 'Project Manager',
            description: 'Manages projects and team members',
            organizationId: organization.id,
          },
          {
            name: 'Developer',
            description: 'Develops and maintains applications',
            organizationId: organization.id,
          },
          {
            name: 'Viewer',
            description: 'Read-only access to resources',
            organizationId: organization.id,
          }
        ]
      });
      console.log('✅ Created sample roles');
    } else {
      console.log('✅ Sample roles already exist');
    }
    
  } catch (error) {
    console.error('❌ Error creating sample roles:', error);
  }
}

async function testRolesAPILogic() {
  console.log('🔧 Testing roles API logic directly...');
  
  try {
    // Get organization
    const organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      console.log('❌ No test organization found');
      return false;
    }
    
    // Test the exact query from the roles API
    const roles = await db.customRole.findMany({
      where: { organizationId: organization.id },
      include: {
        permissions: {
          include: {
            permission: {
              select: {
                name: true,
                displayName: true,
                description: true,
                category: true,
              },
            },
          },
        },
        userRoles: {
          select: {
            userId: true,
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
        parentRole: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        childRoles: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
    
    console.log(`✅ Found ${roles.length} roles`);
    
    if (roles.length > 0) {
      console.log('Sample role:', {
        id: roles[0].id,
        name: roles[0].name,
        description: roles[0].description,
        isSystemRole: roles[0].isSystemRole,
        permissionCount: roles[0].permissions.length,
        userCount: roles[0].userRoles.length
      });
    }
    
    // Test transformation logic
    const transformedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      displayName: role.description || role.name, // Use description as displayName fallback
      description: role.description,
      isSystemRole: role.isSystemRole,
      parentRole: role.parentRole ? {
        ...role.parentRole,
        displayName: role.parentRole.description || role.parentRole.name,
      } : null,
      childRoles: role.childRoles.map(child => ({
        ...child,
        displayName: child.description || child.name,
      })),
      permissions: role.permissions.map(rp => rp.permission),
      userCount: role.userRoles.length,
      users: role.userRoles.map(ur => ur.user),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    }));
    
    console.log('✅ Successfully transformed roles data');
    console.log(`   Transformed ${transformedRoles.length} roles`);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing roles API logic:', error);
    console.error('Error details:', error.message);
    return false;
  }
}

async function main() {
  console.log('🔐 RBAC Roles API Test');
  console.log('======================');
  
  try {
    await db.$connect();
    console.log('✅ Database connected');
    
    // Check if permission exists
    const permissionExists = await checkPermissionExists();
    
    if (!permissionExists) {
      console.log('❌ Required permission does not exist. Run RBAC initialization first.');
      console.log('   Command: npm run rbac:init');
      return;
    }
    
    // Create test user
    await createTestUserWithRolePermission();
    
    // Create sample roles
    await createSampleRoles();
    
    // Test API logic
    const apiTestPassed = await testRolesAPILogic();
    
    if (apiTestPassed) {
      console.log('\n🎉 Roles API test completed successfully!');
      console.log('\nTo test the full API:');
      console.log('1. Start the server: npm run dev');
      console.log('2. Log in as: <EMAIL> / test123');
      console.log('3. Visit: http://localhost:3000/dashboard/rbac/roles');
      console.log('4. Or test API: curl with session cookie');
      console.log('\n✅ The API should now return proper data instead of 500 errors');
    } else {
      console.log('\n❌ Roles API test failed. Check the errors above.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}
