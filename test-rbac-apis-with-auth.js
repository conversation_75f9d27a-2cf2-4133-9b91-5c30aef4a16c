#!/usr/bin/env node

/**
 * Comprehensive RBAC API Test Suite with Authentication
 * Tests all RBAC endpoints with proper authentication
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  baseUrl: BASE_URL,
  timeout: 10000,
  credentials: {
    admin: {
      email: '<EMAIL>',
      password: 'admin123'
    },
    orgAdmin: {
      email: '<EMAIL>', 
      password: 'orgadmin123'
    },
    user: {
      email: '<EMAIL>',
      password: 'user123'
    }
  }
};

class RBACAPITester {
  constructor() {
    this.sessions = {};
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    }[type] || '📋';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async makeRequest(url, options = {}) {
    const fullUrl = url.startsWith('http') ? url : `${TEST_CONFIG.baseUrl}${url}`;
    
    const defaultOptions = {
      timeout: TEST_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    try {
      const response = await fetch(fullUrl, { ...defaultOptions, ...options });
      const data = await response.json().catch(() => ({}));
      
      return {
        status: response.status,
        ok: response.ok,
        data,
        headers: response.headers
      };
    } catch (error) {
      return {
        status: 0,
        ok: false,
        error: error.message,
        data: {}
      };
    }
  }

  async authenticate(userType) {
    const credentials = TEST_CONFIG.credentials[userType];
    if (!credentials) {
      throw new Error(`Unknown user type: ${userType}`);
    }

    this.log(`Authenticating as ${userType}...`);

    // First, get CSRF token
    const csrfResponse = await this.makeRequest('/api/auth/csrf');
    if (!csrfResponse.ok) {
      throw new Error('Failed to get CSRF token');
    }

    const csrfToken = csrfResponse.data.csrfToken;

    // Attempt to sign in
    const signInResponse = await this.makeRequest('/api/auth/callback/credentials', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: credentials.email,
        password: credentials.password,
        csrfToken,
        callbackUrl: `${TEST_CONFIG.baseUrl}/dashboard`,
        json: 'true'
      }).toString()
    });

    // Extract session cookie
    const setCookieHeader = signInResponse.headers.get('set-cookie');
    if (setCookieHeader) {
      const sessionCookie = setCookieHeader
        .split(',')
        .find(cookie => cookie.includes('next-auth.session-token') || cookie.includes('__Secure-next-auth.session-token'))
        ?.split(';')[0];
      
      if (sessionCookie) {
        this.sessions[userType] = sessionCookie;
        this.log(`Authentication successful for ${userType}`, 'success');
        return true;
      }
    }

    // Try alternative authentication method
    const sessionResponse = await this.makeRequest('/api/auth/session');
    if (sessionResponse.ok && sessionResponse.data.user) {
      this.log(`Using existing session for ${userType}`, 'success');
      return true;
    }

    this.log(`Authentication failed for ${userType}`, 'error');
    return false;
  }

  async testEndpoint(name, url, options = {}, expectedStatus = 200, userType = 'admin') {
    this.results.total++;
    
    try {
      const headers = { ...options.headers };
      
      // Add session cookie if available
      if (this.sessions[userType]) {
        headers.Cookie = this.sessions[userType];
      }

      const response = await this.makeRequest(url, {
        ...options,
        headers
      });

      const passed = response.status === expectedStatus;
      
      if (passed) {
        this.results.passed++;
        this.log(`${name}: PASSED (${response.status})`, 'success');
      } else {
        this.results.failed++;
        this.log(`${name}: FAILED (expected ${expectedStatus}, got ${response.status})`, 'error');
        if (response.data.error) {
          this.log(`  Error: ${response.data.error}`, 'error');
        }
      }

      this.results.tests.push({
        name,
        url,
        expected: expectedStatus,
        actual: response.status,
        passed,
        userType,
        response: response.data
      });

      return response;
    } catch (error) {
      this.results.failed++;
      this.log(`${name}: ERROR - ${error.message}`, 'error');
      
      this.results.tests.push({
        name,
        url,
        expected: expectedStatus,
        actual: 0,
        passed: false,
        userType,
        error: error.message
      });
      
      return null;
    }
  }

  async runTests() {
    this.log('🚀 Starting RBAC API Test Suite with Authentication');
    this.log(`Base URL: ${TEST_CONFIG.baseUrl}`);

    try {
      // Test 1: Check if server is running
      this.log('\n📡 Testing server connectivity...');
      const healthCheck = await this.makeRequest('/api/health');
      if (!healthCheck.ok) {
        this.log('Server is not responding. Please ensure the application is running.', 'error');
        return;
      }
      this.log('Server is running', 'success');

      // Test 2: Test unauthenticated access (should fail)
      this.log('\n🔒 Testing unauthenticated access...');
      await this.testEndpoint('GET /api/rbac/users (no auth)', '/api/rbac/users', {}, 401, 'none');
      await this.testEndpoint('GET /api/rbac/roles (no auth)', '/api/rbac/roles', {}, 401, 'none');
      await this.testEndpoint('GET /api/rbac/audit (no auth)', '/api/rbac/audit', {}, 401, 'none');

      // Test 3: Try to authenticate as admin
      this.log('\n👤 Testing authentication...');
      const adminAuth = await this.authenticate('admin');
      
      if (!adminAuth) {
        this.log('Cannot authenticate as admin. Creating test user...', 'warning');
        // Try to create a test admin user
        await this.createTestUser();
      }

      // Test 4: Test authenticated access
      this.log('\n🔐 Testing authenticated RBAC endpoints...');
      
      // RBAC Init endpoints
      await this.testEndpoint('GET /api/rbac/init', '/api/rbac/init', {}, 200, 'admin');
      await this.testEndpoint('POST /api/rbac/init', '/api/rbac/init', { method: 'POST' }, 200, 'admin');

      // Users endpoints
      await this.testEndpoint('GET /api/rbac/users', '/api/rbac/users?limit=5', {}, 200, 'admin');
      
      // Roles endpoints  
      await this.testEndpoint('GET /api/rbac/roles', '/api/rbac/roles', {}, 200, 'admin');
      
      // Audit endpoints
      await this.testEndpoint('GET /api/rbac/audit', '/api/rbac/audit?limit=5', {}, 200, 'admin');

      // Permissions endpoints
      await this.testEndpoint('GET /api/rbac/permissions', '/api/rbac/permissions', {}, 200, 'admin');

      // Test 5: Test permission checking
      this.log('\n🎯 Testing permission checking...');
      await this.testEndpoint('GET /api/rbac/check-permission', '/api/rbac/check-permission?permission=read:user', {}, 200, 'admin');

      // Test 6: Test user permissions
      await this.testEndpoint('GET /api/rbac/user-permissions', '/api/rbac/user-permissions', {}, 200, 'admin');

    } catch (error) {
      this.log(`Test suite error: ${error.message}`, 'error');
    }

    this.printResults();
  }

  async createTestUser() {
    this.log('Attempting to create test admin user...', 'warning');
    
    try {
      const response = await this.makeRequest('/api/register', {
        method: 'POST',
        body: JSON.stringify({
          name: 'Test Admin',
          email: '<EMAIL>',
          password: 'admin123',
          role: 'ADMIN'
        })
      });

      if (response.ok) {
        this.log('Test admin user created successfully', 'success');
        return true;
      } else {
        this.log(`Failed to create test user: ${response.data.error || 'Unknown error'}`, 'error');
        return false;
      }
    } catch (error) {
      this.log(`Error creating test user: ${error.message}`, 'error');
      return false;
    }
  }

  printResults() {
    this.log('\n📊 Test Results Summary');
    this.log('='.repeat(50));
    this.log(`Total Tests: ${this.results.total}`);
    this.log(`Passed: ${this.results.passed}`, 'success');
    this.log(`Failed: ${this.results.failed}`, this.results.failed > 0 ? 'error' : 'success');
    this.log(`Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);

    if (this.results.failed > 0) {
      this.log('\n❌ Failed Tests:', 'error');
      this.results.tests
        .filter(test => !test.passed)
        .forEach(test => {
          this.log(`  - ${test.name}: ${test.error || `Expected ${test.expected}, got ${test.actual}`}`, 'error');
        });
    }

    this.log('\n🎯 Recommendations:');
    if (this.results.failed === 0) {
      this.log('✅ All RBAC APIs are functioning correctly!', 'success');
    } else {
      this.log('⚠️  Some RBAC APIs need attention. Check the failed tests above.', 'warning');
      this.log('💡 Common issues:', 'info');
      this.log('   - Ensure the application server is running');
      this.log('   - Check database connectivity');
      this.log('   - Verify RBAC system is initialized');
      this.log('   - Confirm user authentication is working');
    }
  }
}

// Run the tests
async function main() {
  const tester = new RBACAPITester();
  await tester.runTests();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = RBACAPITester;
