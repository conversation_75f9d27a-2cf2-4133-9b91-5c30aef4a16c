#!/usr/bin/env node

/**
 * Test the RBAC Users API
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const db = new PrismaClient();

async function checkPermissionExists() {
  console.log('🔧 Checking if users permission exists...');
  
  try {
    const permission = await db.permission.findUnique({
      where: { name: 'read:user' }
    });
    
    if (permission) {
      console.log('✅ Permission read:user exists');
      return true;
    } else {
      console.log('❌ Permission read:user does not exist');
      
      // Check what user permissions exist
      const userPermissions = await db.permission.findMany({
        where: { 
          OR: [
            { name: { contains: 'user' } },
            { name: { contains: 'rbac' } }
          ]
        },
        select: { name: true, displayName: true }
      });
      
      console.log('Available user/rbac permissions:');
      userPermissions.forEach(p => console.log(`  - ${p.name}: ${p.displayName}`));
      
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking permission:', error);
    return false;
  }
}

async function createTestUserWithUserPermission() {
  console.log('🔧 Creating test user with user permissions...');
  
  try {
    // Find or create organization
    let organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      organization = await db.organization.create({
        data: {
          name: 'Test Organization',
          status: 'ACTIVE',
          verificationStatus: 'VERIFIED',
        }
      });
    }
    
    // Create admin user with user permissions
    const userExists = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!userExists) {
      const hashedPassword = await bcrypt.hash('test123', 12);
      const user = await db.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Users Test Admin',
          password: hashedPassword,
          role: 'ORGANIZATION_ADMIN',
          organizationId: organization.id,
          emailVerified: new Date(),
          jobTitle: 'Test Administrator',
          departmentName: 'IT Department',
          phoneNumber: '******-0123',
        }
      });
      console.log('✅ Created test user: <EMAIL> / test123');
      return user;
    } else {
      console.log('✅ Test user already exists');
      return userExists;
    }
    
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  }
}

async function createSampleUsersWithRoles() {
  console.log('🔧 Creating sample users with role assignments...');
  
  try {
    // Get organization
    const organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      console.log('❌ No test organization found');
      return;
    }
    
    // Get or create sample roles
    let projectManagerRole = await db.customRole.findFirst({
      where: { name: 'Project Manager', organizationId: organization.id }
    });
    
    if (!projectManagerRole) {
      projectManagerRole = await db.customRole.create({
        data: {
          name: 'Project Manager',
          description: 'Manages projects and team members',
          organizationId: organization.id,
        }
      });
    }
    
    // Create sample users if they don't exist
    const sampleUsers = [
      {
        email: '<EMAIL>',
        name: 'John Doe',
        jobTitle: 'Senior Developer',
        departmentName: 'Engineering',
        phoneNumber: '******-0001',
      },
      {
        email: '<EMAIL>',
        name: 'Jane Smith',
        jobTitle: 'Product Manager',
        departmentName: 'Product',
        phoneNumber: '******-0002',
      }
    ];
    
    for (const userData of sampleUsers) {
      const existingUser = await db.user.findUnique({
        where: { email: userData.email }
      });
      
      if (!existingUser) {
        const hashedPassword = await bcrypt.hash('password123', 12);
        const newUser = await db.user.create({
          data: {
            ...userData,
            password: hashedPassword,
            role: 'ORGANIZATION_USER',
            organizationId: organization.id,
            emailVerified: new Date(),
          }
        });
        
        // Assign role to first user
        if (userData.email === '<EMAIL>') {
          await db.userCustomRole.create({
            data: {
              userId: newUser.id,
              roleId: projectManagerRole.id,
            }
          });
        }
        
        console.log(`✅ Created sample user: ${userData.email}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error creating sample users:', error);
  }
}

async function testUsersAPILogic() {
  console.log('🔧 Testing users API logic directly...');
  
  try {
    // Get organization
    const organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      console.log('❌ No test organization found');
      return false;
    }
    
    // Test the exact query from the users API
    const whereClause = {
      organizationId: organization.id,
    };
    
    const [users, totalCount] = await Promise.all([
      db.user.findMany({
        where: whereClause,
        include: {
          customRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  isSystemRole: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      }),
      db.user.count({ where: whereClause }),
    ]);
    
    console.log(`✅ Found ${users.length} users (total: ${totalCount})`);
    
    if (users.length > 0) {
      console.log('Sample user:', {
        id: users[0].id,
        name: users[0].name,
        email: users[0].email,
        jobTitle: users[0].jobTitle,
        systemRole: users[0].role,
        customRoleCount: users[0].customRoles.length
      });
    }
    
    // Test transformation logic
    const transformedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      jobTitle: user.jobTitle,
      departmentName: user.departmentName,
      phoneNumber: user.phoneNumber,
      systemRole: user.role,
      customRoles: user.customRoles.map(ur => ({
        ...ur.role,
        displayName: ur.role.description || ur.role.name, // Add displayName fallback
      })),
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }));
    
    console.log('✅ Successfully transformed users data');
    console.log(`   Transformed ${transformedUsers.length} users`);
    
    if (transformedUsers.length > 0 && transformedUsers[0].customRoles.length > 0) {
      console.log('   Sample custom role:', {
        name: transformedUsers[0].customRoles[0].name,
        displayName: transformedUsers[0].customRoles[0].displayName,
        description: transformedUsers[0].customRoles[0].description
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error testing users API logic:', error);
    console.error('Error details:', error.message);
    return false;
  }
}

async function main() {
  console.log('🔐 RBAC Users API Test');
  console.log('======================');
  
  try {
    await db.$connect();
    console.log('✅ Database connected');
    
    // Check if permission exists
    const permissionExists = await checkPermissionExists();
    
    if (!permissionExists) {
      console.log('❌ Required permission does not exist. Run RBAC initialization first.');
      console.log('   Command: npm run rbac:init');
      return;
    }
    
    // Create test user
    await createTestUserWithUserPermission();
    
    // Create sample users with roles
    await createSampleUsersWithRoles();
    
    // Test API logic
    const apiTestPassed = await testUsersAPILogic();
    
    if (apiTestPassed) {
      console.log('\n🎉 Users API test completed successfully!');
      console.log('\nTo test the full API:');
      console.log('1. Start the server: npm run dev');
      console.log('2. Log in as: <EMAIL> / test123');
      console.log('3. Visit: http://localhost:3000/dashboard/rbac/users');
      console.log('4. Or test API: curl with session cookie');
      console.log('\n✅ The API should now return proper data instead of 500 errors');
    } else {
      console.log('\n❌ Users API test failed. Check the errors above.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}
