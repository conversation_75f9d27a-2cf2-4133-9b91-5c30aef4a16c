#!/usr/bin/env tsx

/**
 * RBAC System Test Runner
 * 
 * This script tests the RBAC system functionality including:
 * - Basic RBAC service functionality
 * - API endpoint access control
 * - Role-based permissions
 * - Organization isolation
 */

import { db } from './src/lib/db';
import { logger } from './src/lib/logger';
import { runRbacTests, cleanupTestData } from './src/lib/rbac/test-rbac';
import { RbacApiTestSuite } from './src/lib/rbac/api-test-suite';
import { HttpTestRunner } from './src/lib/rbac/http-test-runner';

interface TestSummary {
  testType: string;
  total: number;
  passed: number;
  failed: number;
  duration: number;
}

class RbacSystemTester {
  private summaries: TestSummary[] = [];

  /**
   * Run basic RBAC functionality tests
   */
  async runBasicTests(): Promise<TestSummary> {
    console.log('\n🔧 Running Basic RBAC Tests...');
    const startTime = Date.now();

    try {
      const results = await runRbacTests();
      const passed = results.filter(r => r.passed).length;
      const failed = results.filter(r => !r.passed).length;
      const duration = Date.now() - startTime;

      console.log(`   ✅ Passed: ${passed}`);
      console.log(`   ❌ Failed: ${failed}`);
      
      // Show failed tests
      results.filter(r => !r.passed).forEach(result => {
        console.log(`   ❌ ${result.name}: ${result.error || 'Failed'}`);
      });

      return {
        testType: 'Basic RBAC',
        total: results.length,
        passed,
        failed,
        duration,
      };
    } catch (error) {
      console.log(`   ❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        testType: 'Basic RBAC',
        total: 0,
        passed: 0,
        failed: 1,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Run API access control tests
   */
  async runApiTests(): Promise<TestSummary> {
    console.log('\n🌐 Running API Access Control Tests...');
    const startTime = Date.now();

    try {
      const apiTestSuite = new RbacApiTestSuite();
      await apiTestSuite.setup();
      
      const results = await apiTestSuite.runTests();
      const passed = results.filter(r => r.passed).length;
      const failed = results.filter(r => !r.passed).length;
      const duration = Date.now() - startTime;

      console.log(`   ✅ Passed: ${passed}`);
      console.log(`   ❌ Failed: ${failed}`);

      // Show some sample results
      const sampleResults = results.slice(0, 5);
      sampleResults.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`   ${status} ${result.method} ${result.endpoint} (${result.userRole}): ${result.actualStatus}`);
      });

      if (failed > 0) {
        console.log('\n   Failed tests:');
        results.filter(r => !r.passed).slice(0, 5).forEach(result => {
          console.log(`   ❌ ${result.method} ${result.endpoint} (${result.userRole}): Expected ${result.expectedStatus}, got ${result.actualStatus}`);
        });
      }

      await apiTestSuite.cleanup();

      return {
        testType: 'API Access Control',
        total: results.length,
        passed,
        failed,
        duration,
      };
    } catch (error) {
      console.log(`   ❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        testType: 'API Access Control',
        total: 0,
        passed: 0,
        failed: 1,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Run HTTP endpoint tests
   */
  async runHttpTests(): Promise<TestSummary> {
    console.log('\n🌍 Running HTTP Endpoint Tests...');
    const startTime = Date.now();

    try {
      const httpTestRunner = new HttpTestRunner();
      const results = await httpTestRunner.runHttpTests();
      const passed = results.filter(r => r.passed).length;
      const failed = results.filter(r => !r.passed).length;
      const duration = Date.now() - startTime;

      console.log(`   ✅ Passed: ${passed}`);
      console.log(`   ❌ Failed: ${failed}`);

      // Show role-based access summary
      const roleResults = results.reduce((acc, result) => {
        if (!acc[result.userRole]) {
          acc[result.userRole] = { passed: 0, failed: 0 };
        }
        if (result.passed) {
          acc[result.userRole].passed++;
        } else {
          acc[result.userRole].failed++;
        }
        return acc;
      }, {} as Record<string, { passed: number; failed: number }>);

      console.log('\n   Results by role:');
      Object.entries(roleResults).forEach(([role, stats]) => {
        console.log(`   ${role}: ${stats.passed} passed, ${stats.failed} failed`);
      });

      await httpTestRunner.cleanup();

      return {
        testType: 'HTTP Endpoints',
        total: results.length,
        passed,
        failed,
        duration,
      };
    } catch (error) {
      console.log(`   ❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        testType: 'HTTP Endpoints',
        total: 0,
        passed: 0,
        failed: 1,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Test database connectivity and RBAC schema
   */
  async testDatabaseSchema(): Promise<TestSummary> {
    console.log('\n🗄️  Testing Database Schema...');
    const startTime = Date.now();

    try {
      // Test database connection
      await db.$connect();
      console.log('   ✅ Database connection successful');

      // Check RBAC tables exist
      const tables = [
        'permission',
        'customRole',
        'userCustomRole',
        'rolePermission',
        'permissionGrant',
        'temporaryPermission',
        'permissionUsageLog',
      ];

      let passed = 0;
      let failed = 0;

      for (const table of tables) {
        try {
          // Try to query the table using the correct Prisma client model name
          const count = await (db as any)[table].count();
          console.log(`   ✅ ${table} table exists (${count} records)`);
          passed++;
        } catch (error) {
          console.log(`   ❌ ${table} table missing or inaccessible: ${error instanceof Error ? error.message : 'Unknown error'}`);
          failed++;
        }
      }

      const duration = Date.now() - startTime;

      return {
        testType: 'Database Schema',
        total: tables.length + 1, // +1 for connection test
        passed: passed + 1,
        failed,
        duration,
      };
    } catch (error) {
      console.log(`   ❌ Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        testType: 'Database Schema',
        total: 1,
        passed: 0,
        failed: 1,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🔐 RBAC System Test Suite');
    console.log('=========================');

    const startTime = Date.now();

    // Run all test suites
    this.summaries.push(await this.testDatabaseSchema());
    this.summaries.push(await this.runBasicTests());
    this.summaries.push(await this.runApiTests());
    this.summaries.push(await this.runHttpTests());

    const totalDuration = Date.now() - startTime;

    // Print overall summary
    console.log('\n📊 Overall Test Summary');
    console.log('=======================');

    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;

    this.summaries.forEach(summary => {
      totalTests += summary.total;
      totalPassed += summary.passed;
      totalFailed += summary.failed;
      
      const passRate = summary.total > 0 ? ((summary.passed / summary.total) * 100).toFixed(1) : '0.0';
      console.log(`${summary.testType}: ${summary.passed}/${summary.total} (${passRate}%) - ${summary.duration}ms`);
    });

    console.log('\n📈 Final Results:');
    console.log(`   ✅ Total Passed: ${totalPassed}`);
    console.log(`   ❌ Total Failed: ${totalFailed}`);
    console.log(`   📊 Total Tests: ${totalTests}`);
    console.log(`   ⏱️  Total Duration: ${totalDuration}ms`);

    const overallPassRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : '0.0';
    console.log(`   🎯 Pass Rate: ${overallPassRate}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 All tests passed! RBAC system is functioning correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the RBAC implementation.');
    }

    // Cleanup
    try {
      await cleanupTestData();
      console.log('\n🧹 Test data cleanup completed');
    } catch (error) {
      console.log(`\n⚠️  Cleanup warning: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    await db.$disconnect();
  }
}

// Main execution
async function main() {
  const tester = new RbacSystemTester();
  await tester.runAllTests();
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

export { RbacSystemTester };
